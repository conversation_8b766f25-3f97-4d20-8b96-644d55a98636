# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Admin dashboard (separate repository)
kidz-zone-admin/

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Test and documentation files
test-*.html
*-test.html
test-upload-guide.html
AGE_*.md
SAMPLE_*.md
*_SOLUTION.md
*_COMPLETE.md

# Temporary files
*.tmp
*.temp
*.log

# Environment files
.env
.env.development
.env.production
