export interface Translations {
  [key: string]: {
    [language: string]: string;
  };
}

export const translations: Translations = {
  // Navigation
  'nav.home': {
    'en': 'Home',
    'es': 'Inicio',
    'fr': 'Accueil',
    'de': 'Startseite',
    'it': 'Casa',
    'pt': 'Início',
    'ru': 'Главная',
    'zh': '首页',
    'ja': 'ホーム',
    'ko': '홈',
    'ar': 'الرئيسية',
    'hi': 'होम',
    'ur': 'ہوم',
    'bn': 'হোম',
    'tr': 'Ana <PERSON>',
    'nl': 'Thuis',
    'sv': 'Hem',
    'no': 'Hjem',
    'da': 'Hjem',
    'fi': 'Koti'
  },
  'nav.ageGroups': {
    'en': 'Age Groups',
    'es': 'Grupos de Edad',
    'fr': 'Groupes d\'âge',
    'de': 'Altersgruppen',
    'it': 'Gruppi di Età',
    'pt': 'Grupos Etários',
    'ru': 'Возрастные группы',
    'zh': '年龄组',
    'ja': '年齢グループ',
    'ko': '연령 그룹',
    'ar': 'المجموعات العمرية',
    'hi': 'आयु समूह',
    'ur': 'عمر کے گروپس',
    'bn': 'বয়সের গ্রুপ',
    'tr': 'Yaş Grupları',
    'nl': 'Leeftijdsgroepen',
    'sv': 'Åldersgrupper',
    'no': 'Aldersgrupper',
    'da': 'Aldersgrupper',
    'fi': 'Ikäryhmät'
  },
  'nav.codeStories': {
    'en': 'Code Stories',
    'es': 'Historias de Código',
    'fr': 'Histoires de Code',
    'de': 'Code-Geschichten',
    'it': 'Storie di Codice',
    'pt': 'Histórias de Código',
    'ru': 'Истории кода',
    'zh': '编程故事',
    'ja': 'コードストーリー',
    'ko': '코드 스토리',
    'ar': 'قصص البرمجة',
    'hi': 'कोड कहानियां',
    'ur': 'کوڈ کہانیاں',
    'bn': 'কোড গল্প',
    'tr': 'Kod Hikayeleri',
    'nl': 'Code Verhalen',
    'sv': 'Kodberättelser',
    'no': 'Kodehistorier',
    'da': 'Kodehistorier',
    'fi': 'Kooditarinat'
  },
  'nav.codeVideos': {
    'en': 'Code Videos',
    'es': 'Videos de Código',
    'fr': 'Vidéos de Code',
    'de': 'Code-Videos',
    'it': 'Video di Codice',
    'pt': 'Vídeos de Código',
    'ru': 'Видео кода',
    'zh': '编程视频',
    'ja': 'コードビデオ',
    'ko': '코드 비디오',
    'ar': 'فيديوهات البرمجة',
    'hi': 'कोड वीडियो',
    'ur': 'کوڈ ویڈیوز',
    'bn': 'কোড ভিডিও',
    'tr': 'Kod Videoları',
    'nl': 'Code Video\'s',
    'sv': 'Kodvideor',
    'no': 'Kodevideoer',
    'da': 'Kodevideoer',
    'fi': 'Koodivideot'
  },
  'nav.poems': {
    'en': 'Poems',
    'es': 'Poemas',
    'fr': 'Poèmes',
    'de': 'Gedichte',
    'it': 'Poesie',
    'pt': 'Poemas',
    'ru': 'Стихи',
    'zh': '诗歌',
    'ja': '詩',
    'ko': '시',
    'ar': 'القصائد',
    'hi': 'कविताएं',
    'ur': 'نظمیں',
    'bn': 'কবিতা',
    'tr': 'Şiirler',
    'nl': 'Gedichten',
    'sv': 'Dikter',
    'no': 'Dikt',
    'da': 'Digte',
    'fi': 'Runot'
  },
  'nav.blog': {
    'en': 'Blog',
    'es': 'Blog',
    'fr': 'Blog',
    'de': 'Blog',
    'it': 'Blog',
    'pt': 'Blog',
    'ru': 'Блог',
    'zh': '博客',
    'ja': 'ブログ',
    'ko': '블로그',
    'ar': 'المدونة',
    'hi': 'ब्लॉग',
    'ur': 'بلاگ',
    'bn': 'ব্লগ',
    'tr': 'Blog',
    'nl': 'Blog',
    'sv': 'Blogg',
    'no': 'Blogg',
    'da': 'Blog',
    'fi': 'Blogi'
  },
  'nav.parents': {
    'en': 'For Parents',
    'es': 'Para Padres',
    'fr': 'Pour les Parents',
    'de': 'Für Eltern',
    'it': 'Per i Genitori',
    'pt': 'Para Pais',
    'ru': 'Для родителей',
    'zh': '家长专区',
    'ja': '保護者向け',
    'ko': '부모님을 위해',
    'ar': 'للوالدين',
    'hi': 'माता-पिता के लिए',
    'ur': 'والدین کے لیے',
    'bn': 'অভিভাবকদের জন্য',
    'tr': 'Ebeveynler İçin',
    'nl': 'Voor Ouders',
    'sv': 'För Föräldrar',
    'no': 'For Foreldre',
    'da': 'For Forældre',
    'fi': 'Vanhemmille'
  },
  'nav.educators': {
    'en': 'Educators',
    'es': 'Educadores',
    'fr': 'Éducateurs',
    'de': 'Pädagogen',
    'it': 'Educatori',
    'pt': 'Educadores',
    'ru': 'Педагоги',
    'zh': '教育工作者',
    'ja': '教育者',
    'ko': '교육자',
    'ar': 'المعلمون',
    'hi': 'शिक्षक',
    'ur': 'تعلیم دینے والے',
    'bn': 'শিক্ষাবিদ',
    'tr': 'Eğitimciler',
    'nl': 'Opvoeders',
    'sv': 'Utbildare',
    'no': 'Utdannere',
    'da': 'Uddannere',
    'fi': 'Kasvattajat'
  },
  'nav.families': {
    'en': 'Families',
    'es': 'Familias',
    'fr': 'Familles',
    'de': 'Familien',
    'it': 'Famiglie',
    'pt': 'Famílias',
    'ru': 'Семьи',
    'zh': '家庭',
    'ja': '家族',
    'ko': '가족',
    'ar': 'العائلات',
    'hi': 'परिवार',
    'ur': 'خاندان',
    'bn': 'পরিবার',
    'tr': 'Aileler',
    'nl': 'Gezinnen',
    'sv': 'Familjer',
    'no': 'Familier',
    'da': 'Familier',
    'fi': 'Perheet'
  },
  // Common phrases
  'common.loading': {
    'en': 'Loading...',
    'es': 'Cargando...',
    'fr': 'Chargement...',
    'de': 'Laden...',
    'it': 'Caricamento...',
    'pt': 'Carregando...',
    'ru': 'Загрузка...',
    'zh': '加载中...',
    'ja': '読み込み中...',
    'ko': '로딩 중...',
    'ar': 'جاري التحميل...',
    'hi': 'लोड हो रहा है...',
    'ur': 'لوڈ ہو رہا ہے...',
    'bn': 'লোড হচ্ছে...',
    'tr': 'Yükleniyor...',
    'nl': 'Laden...',
    'sv': 'Laddar...',
    'no': 'Laster...',
    'da': 'Indlæser...',
    'fi': 'Ladataan...'
  },
  'common.backToHome': {
    'en': 'Back to Home',
    'es': 'Volver al Inicio',
    'fr': 'Retour à l\'accueil',
    'de': 'Zurück zur Startseite',
    'it': 'Torna alla Home',
    'pt': 'Voltar ao Início',
    'ru': 'Вернуться на главную',
    'zh': '返回首页',
    'ja': 'ホームに戻る',
    'ko': '홈으로 돌아가기',
    'ar': 'العودة للرئيسية',
    'hi': 'होम पर वापस जाएं',
    'ur': 'ہوم واپس جائیں',
    'bn': 'হোমে ফিরে যান',
    'tr': 'Ana Sayfaya Dön',
    'nl': 'Terug naar Home',
    'sv': 'Tillbaka till Hem',
    'no': 'Tilbake til Hjem',
    'da': 'Tilbage til Hjem',
    'fi': 'Takaisin Kotiin'
  },
  'common.readMore': {
    'en': 'Read More',
    'es': 'Leer Más',
    'fr': 'Lire Plus',
    'de': 'Mehr Lesen',
    'it': 'Leggi di Più',
    'pt': 'Ler Mais',
    'ru': 'Читать далее',
    'zh': '阅读更多',
    'ja': 'もっと読む',
    'ko': '더 읽기',
    'ar': 'اقرأ المزيد',
    'hi': 'और पढ़ें',
    'ur': 'مزید پڑھیں',
    'bn': 'আরও পড়ুন',
    'tr': 'Daha Fazla Oku',
    'nl': 'Lees Meer',
    'sv': 'Läs Mer',
    'no': 'Les Mer',
    'da': 'Læs Mere',
    'fi': 'Lue Lisää'
  },
  'common.watchVideo': {
    'en': 'Watch Video',
    'es': 'Ver Video',
    'fr': 'Regarder la Vidéo',
    'de': 'Video Ansehen',
    'it': 'Guarda Video',
    'pt': 'Assistir Vídeo',
    'ru': 'Смотреть видео',
    'zh': '观看视频',
    'ja': 'ビデオを見る',
    'ko': '비디오 보기',
    'ar': 'مشاهدة الفيديو',
    'hi': 'वीडियो देखें',
    'ur': 'ویڈیو دیکھیں',
    'bn': 'ভিডিও দেখুন',
    'tr': 'Video İzle',
    'nl': 'Video Bekijken',
    'sv': 'Titta på Video',
    'no': 'Se Video',
    'da': 'Se Video',
    'fi': 'Katso Video'
  }
};

export function getTranslation(key: string, language: string = 'en'): string {
  return translations[key]?.[language] || translations[key]?.['en'] || key;
}

export function useTranslation(language: string = 'en') {
  return (key: string) => getTranslation(key, language);
}
