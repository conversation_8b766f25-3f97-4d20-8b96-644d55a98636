import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import UserTracker from "./components/UserTracker";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

// export const metadata: Metadata = {
//   title: "Create Next App",
//   description: "Generated by create next app",
// };

export const metadata: Metadata = {
  title: {
    template: "%s | Kids Zone.",
    default: "BixForge Kids Zone: Childrens Stories Site",
  },
  description: "Official site for the Stories by BixForge Solutions.",
  icons: {
    icon: [
      // `/assets/logo/logo.png?v=${faviconVersion}`,
      // "/favicon.ico",
      { url: "/assets/images/logo.png" },
    ],
  },
};


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <UserTracker />
        {children}
      </body>
    </html>
  );
}
